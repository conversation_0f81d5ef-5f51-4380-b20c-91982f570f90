<template>
  <div class="clock-container">
    <div class="clock-header-wrapper">
      <div class="clock-header">
        Real-Time Clock
      </div>
      <div class="clock-master-toggle">
        <label class="toggle-switch">
          <input
            type="checkbox"
            :checked="settingsStore.settings.showClockInTitleBar"
            @change="handleToggleEnabled"
          />
          <span class="toggle-slider"></span>
        </label>
        <span class="toggle-label">{{ settingsStore.settings.showClockInTitleBar ? 'On' : 'Off' }}</span>
      </div>
    </div>
    <div class="clock-divider"></div>

    <!-- Clock Settings Content (shown when enabled) -->
    <div v-if="settingsStore.settings.showClockInTitleBar" class="clock-content">
      <div class="clock-options">
        <div class="clock-subtitle">Display Options</div>

        <!-- Clock Preview -->
        <div class="setting-row">
          <div class="setting-info">
            <div class="setting-label">Preview</div>
            <div class="setting-description">How the clock will appear in the title bar</div>
          </div>
          <div class="setting-control">
            <div class="clock-preview">
              <div class="preview-clock">
                {{ currentTime }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useSettingsStore } from '../../stores/settingsStore'

const settingsStore = useSettingsStore()

// Current time display
const currentTime = ref('')
let timeInterval: NodeJS.Timeout | null = null

// Update time display
function updateTime() {
  const now = new Date()
  currentTime.value = now.toLocaleTimeString([], { 
    hour: '2-digit', 
    minute: '2-digit',
    second: '2-digit',
    hour12: false 
  })
}

// Handle toggle enabled/disabled
async function handleToggleEnabled(event: Event) {
  const target = event.target as HTMLInputElement
  await settingsStore.updateSetting('showClockInTitleBar', target.checked)
}

// Lifecycle hooks
onMounted(() => {
  updateTime()
  timeInterval = setInterval(updateTime, 1000)
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
    timeInterval = null
  }
})
</script>

<style scoped>
.clock-container {
  border-radius: 16px;
  background-color: var(--color-card-bg);
  border: 1px solid var(--color-border-primary);
  display: flex;
  padding: 32px;
  flex-direction: column;
  align-items: start;
  font-family: 'Montserrat', sans-serif;
}

@media (max-width: 991px) {
  .clock-container {
    padding: 20px;
  }
}

.clock-header-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.clock-header {
  color: var(--color-text-primary);
  font-size: 28px;
  font-weight: 600;
  margin: 0;
  flex: 1;
}

.clock-master-toggle {
  display: flex;
  align-items: center;
  gap: 10px;
}

.toggle-label {
  color: var(--color-text-primary);
  font-size: 13px;
  font-weight: 500;
  min-width: 22px;
}

.clock-divider {
  background-color: var(--color-border-secondary);
  align-self: stretch;
  display: flex;
  margin-top: 24px;
  flex-shrink: 0;
  height: 1px;
}

@media (max-width: 991px) {
  .clock-divider {
    max-width: 100%;
  }
}

.clock-subtitle {
  color: var(--color-text-primary);
  font-size: 16px;
  font-weight: 500;
  margin-top: 20px;
}

.clock-content {
  width: 100%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.clock-options {
  width: 100%;
}

.setting-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid var(--color-border-secondary);
}

.setting-row:last-child {
  border-bottom: none;
}

.setting-info {
  flex: 1;
}

.setting-label {
  color: var(--color-text-primary);
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;
}

.setting-description {
  color: var(--color-text-secondary);
  font-size: 12px;
  line-height: 1.4;
}

.setting-control {
  margin-left: 16px;
}

.clock-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  border-radius: 8px;
  background-color: var(--color-bg-secondary);
  border: 1px solid var(--color-border-primary);
}

.preview-clock {
  color: var(--color-text-primary);
  font-size: 13px;
  font-weight: 500;
  font-family: 'Courier New', monospace;
  letter-spacing: 0.5px;
}

/* Toggle Switch Styles */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 42px;
  height: 22px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--color-border-primary);
  transition: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 22px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 3px;
  bottom: 3px;
  background-color: var(--color-bg-primary);
  transition: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 50%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

input:checked + .toggle-slider {
  background-color: var(--color-primary);
}

input:focus + .toggle-slider {
  box-shadow: 0 0 1px var(--color-primary);
}

input:checked + .toggle-slider:before {
  transform: translateX(20px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .setting-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .setting-control {
    margin-left: 0;
    align-self: stretch;
  }

  .clock-preview {
    justify-content: flex-start;
  }
}
</style>
