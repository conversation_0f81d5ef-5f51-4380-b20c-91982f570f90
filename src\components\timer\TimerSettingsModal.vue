<template>
  <Teleport to="body">
    <div class="modal-overlay">
      <div class="modal-content">
        <div class="modal-header">
          <h3>Timer Settings</h3>
          <div class="close-button" @click="handleClose">
            <img src="/icons/close-icon.svg" alt="Close" />
          </div>
        </div>

        <div class="modal-body">
          <div class="settings-form">
            <!-- Timer Durations Section -->
            <div class="form-section">
              <h4 class="section-title">Timer Durations</h4>
              <div class="form-grid">
                <div class="form-field">
                  <label>Pomodoro Duration</label>
                  <div class="input-with-unit">
                    <input
                      type="number"
                      v-model.number="settings.pomodoroTime"
                      min="1"
                      max="60"
                      step="1"
                      class="form-input"
                      @input="validatePomodoroTime"
                      @blur="validatePomodoroTime"
                    />
                    <span class="input-unit">minutes</span>
                  </div>
                </div>

                <div class="form-field">
                  <label>Short Break Duration</label>
                  <div class="input-with-unit">
                    <input
                      type="number"
                      v-model.number="settings.shortBreakTime"
                      min="1"
                      max="60"
                      step="1"
                      class="form-input"
                      @input="validateShortBreakTime"
                      @blur="validateShortBreakTime"
                    />
                    <span class="input-unit">minutes</span>
                  </div>
                </div>

                <div class="form-field">
                  <label>Long Break Duration</label>
                  <div class="input-with-unit">
                    <input
                      type="number"
                      v-model.number="settings.longBreakTime"
                      min="1"
                      max="60"
                      step="1"
                      class="form-input"
                      @input="validateLongBreakTime"
                      @blur="validateLongBreakTime"
                    />
                    <span class="input-unit">minutes</span>
                  </div>
                </div>

                <div class="form-field">
                  <label>Long Break Interval</label>
                  <div class="input-with-unit">
                    <input
                      type="number"
                      v-model.number="settings.longBreakInterval"
                      min="1"
                      max="10"
                      step="1"
                      class="form-input"
                      @input="validateLongBreakInterval"
                      @blur="validateLongBreakInterval"
                    />
                    <span class="input-unit">pomodoros</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Auto-Start Options Section -->
            <div class="form-section">
              <h4 class="section-title">Auto-Start Options</h4>
              <div class="checkbox-grid">
                <div class="checkbox-field">
                  <div class="checkbox-wrapper">
                    <input
                      type="checkbox"
                      v-model="settings.autoStartBreaks"
                      class="form-checkbox"
                      id="auto-start-breaks"
                    />
                    <label for="auto-start-breaks" class="checkbox-label">
                      <span class="checkbox-title">Auto Start Breaks</span>
                      <span class="checkbox-description">Automatically start break timers when pomodoro ends</span>
                    </label>
                  </div>
                </div>

                <div class="checkbox-field">
                  <div class="checkbox-wrapper">
                    <input
                      type="checkbox"
                      v-model="settings.autoStartPomodoros"
                      class="form-checkbox"
                      id="auto-start-pomodoros"
                    />
                    <label for="auto-start-pomodoros" class="checkbox-label">
                      <span class="checkbox-title">Auto Start Pomodoros</span>
                      <span class="checkbox-description">Automatically start pomodoro timers when break ends</span>
                    </label>
                  </div>
                </div>
              </div>
            </div>

            <!-- Sound Notifications Section -->
            <div class="form-section">
              <h4 class="section-title">Sound Notifications</h4>
              <div class="checkbox-grid">
                <div class="checkbox-field">
                  <div class="checkbox-wrapper">
                    <input
                      type="checkbox"
                      v-model="settings.timerSoundsEnabled"
                      class="form-checkbox"
                      id="timer-sounds-enabled"
                    />
                    <label for="timer-sounds-enabled" class="checkbox-label">
                      <span class="checkbox-title">Enable Timer Sounds</span>
                      <span class="checkbox-description">Play notification sounds when timers complete</span>
                    </label>
                  </div>
                </div>

                <div v-if="settings.timerSoundsEnabled" class="sound-settings">
                  <div class="form-field">
                    <label>Pomodoro Complete Sound</label>
                    <div class="sound-selector">
                      <select v-model="settings.pomodoroCompleteSound" class="form-select">
                        <option value="default">Default Bell</option>
                        <option value="chime">Soft Chime</option>
                        <option value="ding">Classic Ding</option>
                        <option value="none">No Sound</option>
                      </select>
                      <button @click="testSound('pomodoro-complete')" class="btn btn-test">Test</button>
                    </div>
                  </div>

                  <div class="form-field">
                    <label>Break Complete Sound</label>
                    <div class="sound-selector">
                      <select v-model="settings.breakCompleteSound" class="form-select">
                        <option value="default">Default Bell</option>
                        <option value="chime">Soft Chime</option>
                        <option value="ding">Classic Ding</option>
                        <option value="none">No Sound</option>
                      </select>
                      <button @click="testSound('break-complete')" class="btn btn-test">Test</button>
                    </div>
                  </div>

                  <div class="form-field">
                    <label>Volume</label>
                    <div class="volume-control">
                      <input
                        type="range"
                        min="0"
                        max="1"
                        step="0.1"
                        v-model.number="settings.soundVolume"
                        class="volume-slider"
                      />
                      <span class="volume-display">{{ Math.round(settings.soundVolume * 100) }}%</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <div class="modal-footer-right">
            <button class="btn btn-secondary" @click="handleClose">Cancel</button>
            <button class="btn btn-primary" @click="handleSave">Save Settings</button>
          </div>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script lang="ts">
import { defineComponent, reactive, onMounted, onBeforeUnmount, watch } from 'vue';
import { useTimerAudio } from '../../composables/useTimerAudio';

export default defineComponent({
  name: 'TimerSettingsModal',
  props: {
    pomodoroTime: {
      type: Number,
      required: true
    },
    shortBreakTime: {
      type: Number,
      required: true
    },
    longBreakTime: {
      type: Number,
      required: true
    },
    longBreakInterval: {
      type: Number,
      required: true
    },
    autoStartBreaks: {
      type: Boolean,
      default: true
    },
    autoStartPomodoros: {
      type: Boolean,
      default: true
    },
    timerSoundsEnabled: {
      type: Boolean,
      default: true
    },
    pomodoroCompleteSound: {
      type: String,
      default: 'default'
    },
    breakCompleteSound: {
      type: String,
      default: 'default'
    },
    timerStartSound: {
      type: String,
      default: 'none'
    },
    soundVolume: {
      type: Number,
      default: 0.7
    }
  },
  emits: ['close', 'update-settings'],
  setup(props, { emit }) {
    // Create a reactive copy of the props
    const settings = reactive({
      pomodoroTime: Math.round(props.pomodoroTime / 60),
      shortBreakTime: Math.round(props.shortBreakTime / 60),
      longBreakTime: Math.round(props.longBreakTime / 60),
      longBreakInterval: props.longBreakInterval,
      autoStartBreaks: props.autoStartBreaks,
      autoStartPomodoros: props.autoStartPomodoros,
      timerSoundsEnabled: props.timerSoundsEnabled,
      pomodoroCompleteSound: props.pomodoroCompleteSound,
      breakCompleteSound: props.breakCompleteSound,
      timerStartSound: props.timerStartSound,
      soundVolume: props.soundVolume
    });

    // Initialize audio service
    const audio = useTimerAudio();

    // Test sound function for preview functionality
    const testSound = async (soundId: string) => {
      await audio.testSound(soundId);
    };

    // Watch for volume changes to update global volume in real-time
    watch(() => settings.soundVolume, (newVolume) => {
      audio.updateGlobalVolume(newVolume);
    });

    // Event handlers following the established modal pattern
    const handleClose = () => {
      emit('close');
    };

    // Validation functions
    const validatePomodoroTime = () => {
      if (settings.pomodoroTime) {
        settings.pomodoroTime = Math.round(settings.pomodoroTime);
        if (settings.pomodoroTime < 1) settings.pomodoroTime = 1;
        if (settings.pomodoroTime > 60) settings.pomodoroTime = 60;
      }
    };

    const validateShortBreakTime = () => {
      if (settings.shortBreakTime) {
        settings.shortBreakTime = Math.round(settings.shortBreakTime);
        if (settings.shortBreakTime < 1) settings.shortBreakTime = 1;
        if (settings.shortBreakTime > 60) settings.shortBreakTime = 60;
      }
    };

    const validateLongBreakTime = () => {
      if (settings.longBreakTime) {
        settings.longBreakTime = Math.round(settings.longBreakTime);
        if (settings.longBreakTime < 1) settings.longBreakTime = 1;
        if (settings.longBreakTime > 60) settings.longBreakTime = 60;
      }
    };

    const validateLongBreakInterval = () => {
      if (settings.longBreakInterval) {
        settings.longBreakInterval = Math.round(settings.longBreakInterval);
        if (settings.longBreakInterval < 1) settings.longBreakInterval = 1;
        if (settings.longBreakInterval > 10) settings.longBreakInterval = 10;
      }
    };

    const handleSave = () => {
      // Final validation before saving
      validatePomodoroTime();
      validateShortBreakTime();
      validateLongBreakTime();
      validateLongBreakInterval();

      emit('update-settings', {
        pomodoroTime: settings.pomodoroTime * 60,
        shortBreakTime: settings.shortBreakTime * 60,
        longBreakTime: settings.longBreakTime * 60,
        longBreakInterval: settings.longBreakInterval,
        autoStartBreaks: settings.autoStartBreaks,
        autoStartPomodoros: settings.autoStartPomodoros,
        timerSoundsEnabled: settings.timerSoundsEnabled,
        pomodoroCompleteSound: settings.pomodoroCompleteSound,
        breakCompleteSound: settings.breakCompleteSound,
        timerStartSound: settings.timerStartSound,
        soundVolume: settings.soundVolume
      });
      emit('close');
    };

    // Escape key handler
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        emit('close');
      }
    };

    // Add escape key listener on mount
    onMounted(() => {
      document.addEventListener('keydown', handleEscapeKey);
    });

    // Remove escape key listener on unmount
    onBeforeUnmount(() => {
      document.removeEventListener('keydown', handleEscapeKey);
    });

    return {
      settings,
      handleClose,
      handleSave,
      validatePomodoroTime,
      validateShortBreakTime,
      validateLongBreakTime,
      validateLongBreakInterval,
      testSound
    };
  }
});
</script>

<style scoped>
/* Modal Overlay */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--color-modal-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
}

/* Modal Content */
.modal-content {
  background-color: var(--color-modal-bg);
  border-radius: 16px;
  box-shadow: 0 8px 24px var(--color-card-shadow);
  width: 550px;
  max-width: 90%;
  display: flex;
  flex-direction: column;
  font-family: 'Montserrat', sans-serif;
  overflow: hidden;
  max-height: 90vh;
}

/* Modal Header */
.modal-header {
  padding: 16px 40px;
  border-bottom: 1px solid var(--color-modal-border);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-header h3 {
  margin: 0;
  font-size: 28px;
  font-weight: 600;
  color: var(--color-text-primary);
  text-align: center;
}

.close-button {
  position: absolute;
  top: 24px;
  right: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.close-button img {
  width: 24px;
  height: 24px;
}

/* Modal Body */
.modal-body {
  padding: 24px 40px;
  flex: 1;
  overflow-y: auto;
}

.settings-form {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.form-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.section-title {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text-primary);
  padding-bottom: 8px;
  border-bottom: 1px solid var(--color-border-secondary);
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px 24px;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-field label {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-text-primary);
  margin: 0;
}

.input-with-unit {
  position: relative;
  display: flex;
  align-items: center;
}

.form-input {
  padding: 12px 16px;
  padding-right: 100px;
  border: 1px solid var(--color-input-border);
  border-radius: 8px;
  background-color: var(--color-input-bg);
  font-size: 14px;
  color: var(--color-input-text);
  font-family: 'Montserrat', sans-serif;
  transition: border-color 0.2s ease;
  width: 100%;
  box-sizing: border-box;
}

.form-input:focus {
  outline: none;
  border-color: var(--color-input-focus);
  box-shadow: 0 0 0 2px var(--color-nav-item-active);
}

.input-unit {
  position: absolute;
  right: 16px;
  font-size: 12px;
  color: var(--color-text-secondary);
  font-weight: 500;
  pointer-events: none;
}

.checkbox-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.checkbox-field {
  display: flex;
  align-items: flex-start;
}

.checkbox-wrapper {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  width: 100%;
}

.form-checkbox {
  width: 18px;
  height: 18px;
  cursor: pointer;
  margin-top: 2px;
  flex-shrink: 0;
}

.checkbox-label {
  display: flex;
  flex-direction: column;
  gap: 4px;
  cursor: pointer;
  flex: 1;
}

.checkbox-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-text-primary);
}

.checkbox-description {
  font-size: 12px;
  color: var(--color-text-secondary);
  line-height: 1.4;
}

/* Modal Footer */
.modal-footer {
  padding: 16px 40px;
  border-top: 1px solid var(--color-border-primary);
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.modal-footer-right {
  display: flex;
  gap: 16px;
}

.btn {
  padding: 10px 24px;
  min-width: 120px;
  border-radius: 10px;
  font-family: 'Montserrat', sans-serif;
  font-weight: 500;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}

.btn-secondary {
  background-color: var(--color-btn-secondary-bg);
  color: var(--color-btn-secondary-text);
  border: 1px solid var(--color-btn-secondary-border);
}

.btn-secondary:hover {
  background-color: var(--color-btn-secondary-hover);
}

.btn-primary {
  background-color: var(--color-btn-primary-bg);
  color: var(--color-btn-primary-text);
  border: 1px solid var(--color-btn-primary-bg);
}

.btn-primary:hover {
  background-color: var(--color-btn-primary-hover);
}

/* Responsive Design */
@media (max-width: 768px) {
  .modal-content {
    width: 95%;
    margin: 20px;
  }

  .form-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 16px 20px;
  }

  .modal-header h3 {
    font-size: 24px;
  }

  .section-title {
    font-size: 16px;
  }
}

@media (max-width: 640px) {
  .modal-content {
    width: 100%;
    height: 100%;
    border-radius: 0;
    max-height: 100vh;
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 12px 16px;
  }

  .settings-form {
    gap: 24px;
  }

  .modal-footer-right {
    width: 100%;
    flex-direction: column;
    gap: 12px;
  }

  .btn {
    width: 100%;
  }

  .checkbox-wrapper {
    gap: 8px;
  }

  .checkbox-title {
    font-size: 13px;
  }

  .checkbox-description {
    font-size: 11px;
  }
}

/* Sound Settings Styles */
.sound-settings {
  margin-top: 1rem;
  padding-left: 1.5rem;
  border-left: 2px solid var(--color-border-secondary);
}

.sound-selector {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.form-select {
  padding: 12px 16px;
  border: 1px solid var(--color-input-border);
  border-radius: 8px;
  background-color: var(--color-input-bg);
  font-size: 14px;
  color: var(--color-input-text);
  font-family: 'Montserrat', sans-serif;
  transition: border-color 0.2s ease;
  flex: 1;
  box-sizing: border-box;
}

.form-select:focus {
  outline: none;
  border-color: var(--color-input-focus);
  box-shadow: 0 0 0 2px var(--color-nav-item-active);
}

.btn-test {
  padding: 0.25rem 0.75rem;
  font-size: 0.875rem;
  background: var(--color-btn-primary-bg);
  color: var(--color-btn-primary-text);
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
  white-space: nowrap;
}

.btn-test:hover {
  background: var(--color-btn-primary-hover);
}

.volume-control {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.volume-slider {
  flex: 1;
  height: 4px;
  background: var(--color-border-secondary);
  border-radius: 2px;
  outline: none;
  appearance: none;
  cursor: pointer;
}

.volume-slider::-webkit-slider-thumb {
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: var(--color-btn-primary-bg);
  cursor: pointer;
}

.volume-slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: var(--color-btn-primary-bg);
  cursor: pointer;
  border: none;
}

.volume-display {
  min-width: 3rem;
  text-align: right;
  font-size: 0.875rem;
  color: var(--color-text-secondary);
}
</style>
