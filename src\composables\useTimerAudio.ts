// Timer audio service composable
import { ref, computed, readonly } from 'vue'
import { useSettingsStore } from '../stores/settingsStore'

interface SoundMap {
  [key: string]: HTMLAudioElement
}

export interface TimerAudioService {
  playSound(soundId: string, volume?: number): Promise<void>
  preloadSounds(): Promise<void>
  updateGlobalVolume(volume: number): void
  testSound(soundId: string): Promise<void>
  isEnabled: Computed<boolean>
  isLoading: Readonly<Ref<boolean>>
  loadError: Readonly<Ref<string | null>>
}

export function useTimerAudio() {
  const settingsStore = useSettingsStore()
  const sounds = ref<SoundMap>({})
  const isLoading = ref(false)
  const loadError = ref<string | null>(null)

  // Sound file mappings
  const soundFiles = {
    'pomodoro-complete': '/sounds/pomodoro-complete.mp3',
    'break-complete': '/sounds/break-complete.mp3',
    'timer-start': '/sounds/timer-start.mp3',
    'session-complete': '/sounds/session-complete.mp3'
  }

  // Computed properties
  const isEnabled = computed(() => settingsStore.settings.timerSoundsEnabled)
  const globalVolume = computed(() => settingsStore.settings.soundVolume)

  // Preload all sound files
  async function preloadSounds(): Promise<void> {
    if (isLoading.value) return

    isLoading.value = true
    loadError.value = null

    try {
      const loadPromises = Object.entries(soundFiles).map(([key, path]) => {
        return new Promise<void>((resolve, reject) => {
          const audio = new Audio(path)
          audio.preload = 'auto'
          audio.volume = globalVolume.value

          audio.addEventListener('canplaythrough', () => {
            sounds.value[key] = audio
            resolve()
          })

          audio.addEventListener('error', (e) => {
            console.warn(`Failed to load sound: ${key}`, e)
            // Don't reject - allow app to continue without this sound
            resolve()
          })
        })
      })

      await Promise.all(loadPromises)
      console.log('✅ Audio preloading completed')
    } catch (error) {
      loadError.value = 'Failed to load audio files'
      console.error('❌ Audio preloading failed:', error)
    } finally {
      isLoading.value = false
    }
  }

  // Play a specific sound
  async function playSound(soundId: string, volume?: number): Promise<void> {
    if (!isEnabled.value) {
      console.log('🔇 Audio disabled, skipping sound:', soundId)
      return
    }

    const audio = sounds.value[soundId]
    if (!audio) {
      console.warn('🔊 Sound not found:', soundId)
      return
    }

    try {
      // Reset audio to beginning
      audio.currentTime = 0

      // Set volume (use provided volume or global setting)
      audio.volume = volume !== undefined ? volume : globalVolume.value

      // Play the sound
      await audio.play()
      console.log('🔊 Played sound:', soundId)
    } catch (error) {
      console.error('❌ Failed to play sound:', soundId, error)
    }
  }

  // Update volume for all loaded sounds
  function updateGlobalVolume(volume: number): void {
    Object.values(sounds.value).forEach(audio => {
      audio.volume = volume
    })
  }

  // Test a sound (for settings preview)
  async function testSound(soundId: string): Promise<void> {
    await playSound(soundId, globalVolume.value)
  }

  return {
    // State
    isLoading: readonly(isLoading),
    loadError: readonly(loadError),
    isEnabled,

    // Methods
    preloadSounds,
    playSound,
    testSound,
    updateGlobalVolume
  }
}
