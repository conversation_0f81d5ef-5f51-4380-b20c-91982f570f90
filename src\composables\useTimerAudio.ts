// Timer audio service composable
import { ref, computed, readonly } from 'vue'
import { useSettingsStore } from '../stores/settingsStore'

interface SoundMap {
  [key: string]: HTMLAudioElement
}

export interface TimerAudioService {
  playSound(soundId: string, volume?: number): Promise<void>
  preloadSounds(): Promise<void>
  updateGlobalVolume(volume: number): void
  testSound(soundId: string): Promise<void>
  isEnabled: Computed<boolean>
  isLoading: Readonly<Ref<boolean>>
  loadError: Readonly<Ref<string | null>>
}

export function useTimerAudio() {
  const settingsStore = useSettingsStore()
  const sounds = ref<SoundMap>({})
  const isLoading = ref(false)
  const loadError = ref<string | null>(null)
  const audioSupported = ref(true)

  // Sound file mappings
  const soundFiles = {
    'pomodoro-complete': '/sounds/pomodoro-complete.mp3',
    'break-complete': '/sounds/break-complete.mp3',
    'timer-start': '/sounds/timer-start.mp3',
    'session-complete': '/sounds/session-complete.mp3'
  }

  // Computed properties
  const isEnabled = computed(() => settingsStore.settings.timerSoundsEnabled && audioSupported.value)
  const globalVolume = computed(() => settingsStore.settings.soundVolume)

  // Check audio capability
  function checkAudioSupport(): boolean {
    try {
      const audio = new Audio()
      return !!(audio.canPlayType && audio.canPlayType('audio/mpeg'))
    } catch (error) {
      console.warn('🔇 Audio not supported in this environment:', error)
      return false
    }
  }

  // Preload all sound files
  async function preloadSounds(): Promise<void> {
    if (isLoading.value) return

    // Check audio support first
    audioSupported.value = checkAudioSupport()
    if (!audioSupported.value) {
      console.warn('🔇 Audio not supported, skipping sound preloading')
      loadError.value = 'Audio not supported in this environment'
      return
    }

    isLoading.value = true
    loadError.value = null

    try {
      const loadPromises = Object.entries(soundFiles).map(([key, path]) => {
        return new Promise<void>((resolve, reject) => {
          const audio = new Audio(path)
          audio.preload = 'auto'
          audio.volume = globalVolume.value

          audio.addEventListener('canplaythrough', () => {
            sounds.value[key] = audio
            resolve()
          })

          audio.addEventListener('error', (e) => {
            console.warn(`Failed to load sound: ${key}`, e)
            // Don't reject - allow app to continue without this sound
            resolve()
          })
        })
      })

      await Promise.all(loadPromises)
      console.log('✅ Audio preloading completed')
    } catch (error) {
      loadError.value = 'Failed to load audio files'
      console.error('❌ Audio preloading failed:', error)
    } finally {
      isLoading.value = false
    }
  }

  // Play a specific sound
  async function playSound(soundId: string, volume?: number): Promise<void> {
    // Check if audio is enabled and supported
    if (!isEnabled.value) {
      console.log('🔇 Audio disabled, skipping sound:', soundId)
      return
    }

    if (!audioSupported.value) {
      console.log('🔇 Audio not supported, skipping sound:', soundId)
      return
    }

    const audio = sounds.value[soundId]
    if (!audio) {
      console.warn('🔊 Sound not found:', soundId, '- continuing without audio')
      return
    }

    try {
      // Reset audio to beginning
      audio.currentTime = 0

      // Set volume (use provided volume or global setting)
      const targetVolume = volume !== undefined ? volume : globalVolume.value
      audio.volume = Math.max(0, Math.min(1, targetVolume)) // Clamp volume between 0 and 1

      // Play the sound
      await audio.play()
      console.log('🔊 Played sound:', soundId)
    } catch (error) {
      // Handle specific audio errors gracefully
      if (error instanceof DOMException) {
        if (error.name === 'NotAllowedError') {
          console.warn('🔇 Audio playback blocked by browser policy for:', soundId)
        } else if (error.name === 'NotSupportedError') {
          console.warn('🔇 Audio format not supported for:', soundId)
          audioSupported.value = false
        } else {
          console.warn('🔇 Audio playback failed for:', soundId, error.message)
        }
      } else {
        console.error('❌ Unexpected error playing sound:', soundId, error)
      }
      // Continue execution - don't let audio failures break the app
    }
  }

  // Update volume for all loaded sounds
  function updateGlobalVolume(volume: number): void {
    Object.values(sounds.value).forEach(audio => {
      audio.volume = volume
    })
  }

  // Test a sound (for settings preview)
  async function testSound(soundId: string): Promise<void> {
    try {
      await playSound(soundId, globalVolume.value)
    } catch (error) {
      console.warn('🔇 Test sound failed:', soundId, error)
      // Don't throw - just log the error
    }
  }

  return {
    // State
    isLoading: readonly(isLoading),
    loadError: readonly(loadError),
    audioSupported: readonly(audioSupported),
    isEnabled,

    // Methods
    preloadSounds,
    playSound,
    testSound,
    updateGlobalVolume
  }
}
