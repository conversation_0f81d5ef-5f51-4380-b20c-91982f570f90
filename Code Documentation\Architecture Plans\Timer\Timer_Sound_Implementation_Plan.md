# Timer Sound Implementation Plan

## Overview
This document outlines the plan for incorporating sound notifications into the Noti app's timer functionality, including configurable settings to enable/disable sounds for different timer events.

## Current Architecture Analysis

### Timer System Components
- **Timer Store** (`src/stores/timerStore.ts`): Core timer state management with Pinia
- **Pomodoro Timer Component** (`src/components/timer/PomodoroTimer.vue`): Main UI component
- **Timer Settings Modal** (`src/components/timer/TimerSettingsModal.vue`): Configuration interface
- **Timer API** (`electron/main/api/timer-api.ts`): Backend timer operations
- **Settings Store** (`src/stores/settingsStore.ts`): Application-wide settings management

### Key Integration Points
1. **Timer Completion Handler** (`timerStore.ts:466`): `handleTimerComplete()` function
2. **Timer Settings System**: Existing modal and database schema for timer preferences
3. **Settings Architecture**: Established pattern for app-wide settings with database persistence

## Audio Implementation Options Analysis

### Option 1: HTML5 Audio API (Recommended)
**Pros:**
- Simple implementation with `<audio>` elements
- Good browser support and Electron compatibility
- Lightweight and performant
- Easy to preload sounds for instant playback

**Cons:**
- Subject to autoplay policies (mitigated in Electron)
- Limited advanced audio features

**Implementation:**
```javascript
const audio = new Audio('/sounds/timer-complete.mp3');
audio.preload = 'auto';
audio.play().catch(error => console.error('Audio playback failed:', error));
```

### Option 2: Web Audio API
**Pros:**
- Advanced audio control and effects
- Better performance for complex audio scenarios
- Precise timing control

**Cons:**
- More complex implementation
- Overkill for simple notification sounds
- Larger learning curve

### Option 3: Native Electron Audio
**Pros:**
- System-level audio integration
- Bypasses web audio limitations

**Cons:**
- Platform-specific implementation required
- More complex setup and maintenance

**Recommendation:** HTML5 Audio API for simplicity and reliability.

## Sound Settings Architecture Design

### Database Schema Integration
Use existing `settings` table with key-value pairs (no schema changes needed):

The codebase uses a flexible settings system with a `settings` table:
```sql
-- Existing settings table structure:
-- settings (id, key TEXT, value_json TEXT, category TEXT, updated_at)
```

Sound settings will be stored as individual key-value pairs:
- `timer_sounds_enabled` → boolean
- `pomodoro_complete_sound` → string  
- `break_complete_sound` → string
- `timer_start_sound` → string
- `sound_volume` → number

### Settings Store Integration
Extend `AppSettings` interface in `src/stores/settingsStore.ts`:

```typescript
export interface AppSettings {
  // ... existing settings
  
  // Timer sound settings
  timerSoundsEnabled: boolean
  pomodoroCompleteSound: string
  breakCompleteSound: string
  timerStartSound: string
  soundVolume: number
}

// Add to frontendToDbKeyMap (lines 207-227):
const frontendToDbKeyMap = {
  // ... existing mappings
  timerSoundsEnabled: 'timer_sounds_enabled',
  pomodoroCompleteSound: 'pomodoro_complete_sound',
  breakCompleteSound: 'break_complete_sound', 
  timerStartSound: 'timer_start_sound',
  soundVolume: 'sound_volume'
}

// Add to default settings (lines 42-71):
const settings = ref<AppSettings>({
  // ... existing defaults
  timerSoundsEnabled: true,
  pomodoroCompleteSound: 'default',
  breakCompleteSound: 'default',
  timerStartSound: 'none',
  soundVolume: 0.7
})

// Update resetToDefaults function (lines 107-131):
async function resetToDefaults() {
  const defaultSettings: AppSettings = {
    // ... existing defaults
    timerSoundsEnabled: true,
    pomodoroCompleteSound: 'default',
    breakCompleteSound: 'default', 
    timerStartSound: 'none',
    soundVolume: 0.7
  }
  // ... rest of reset logic
}
```

### Settings UI Component
Extend existing `TimerSettingsModal.vue` (add after line 122, before modal footer):

```vue
<!-- Add new Sound Settings section -->
<div class="form-section">
  <h4 class="section-title">Sound Notifications</h4>
  <div class="checkbox-grid">
    <div class="checkbox-field">
      <div class="checkbox-wrapper">
        <input
          type="checkbox"
          v-model="settings.timerSoundsEnabled"
          class="form-checkbox"
          id="timer-sounds-enabled"
        />
        <label for="timer-sounds-enabled" class="checkbox-label">
          <span class="checkbox-title">Enable Timer Sounds</span>
          <span class="checkbox-description">Play notification sounds when timers complete</span>
        </label>
      </div>
    </div>

    <div v-if="settings.timerSoundsEnabled" class="sound-settings">
      <div class="form-field">
        <label>Pomodoro Complete Sound</label>
        <div class="sound-selector">
          <select v-model="settings.pomodoroCompleteSound" class="form-select">
            <option value="default">Default Bell</option>
            <option value="chime">Soft Chime</option>
            <option value="ding">Classic Ding</option>
            <option value="none">No Sound</option>
          </select>
          <button @click="testSound('pomodoro-complete')" class="btn btn-test">Test</button>
        </div>
      </div>

      <div class="form-field">
        <label>Break Complete Sound</label>
        <div class="sound-selector">
          <select v-model="settings.breakCompleteSound" class="form-select">
            <option value="default">Default Bell</option>
            <option value="chime">Soft Chime</option>
            <option value="ding">Classic Ding</option>
            <option value="none">No Sound</option>
          </select>
          <button @click="testSound('break-complete')" class="btn btn-test">Test</button>
        </div>
      </div>

      <div class="form-field">
        <label>Volume</label>
        <div class="volume-control">
          <input
            type="range"
            min="0"
            max="1"
            step="0.1"
            v-model.number="settings.soundVolume"
            class="volume-slider"
          />
          <span class="volume-display">{{ Math.round(settings.soundVolume * 100) }}%</span>
        </div>
      </div>
    </div>
  </div>
</div>
```

## Audio File Management Plan

### File Storage Location
- **Development & Production**: `public/sounds/` directory (following existing pattern with icons in `public/icons/`)
- **Custom Sounds**: User data directory for future extensibility (similar to media files)

### Supported Audio Formats
- **Primary**: MP3 (universal support, good compression)
- **Secondary**: WAV (uncompressed, higher quality)
- **Fallback**: OGG (open source alternative)

### Default Sound Library
Curate a small collection of professional timer sounds in `public/sounds/`:
1. **pomodoro-complete.mp3** - Classic notification bell for work completion
2. **break-complete.mp3** - Gentle chime for break completion
3. **timer-start.mp3** - Subtle start sound (optional)
4. **session-complete.mp3** - Satisfying full session completion sound

### File Size Considerations
- Keep individual files under 100KB
- Total sound library under 500KB
- Use appropriate compression settings

## Implementation Plan

### Phase 1: Core Audio System
1. Create audio service/composable (`src/composables/useAudio.ts`)
2. Implement basic sound playback functionality
3. Add sound preloading for performance
4. Handle audio playback errors gracefully

### Phase 2: Settings Integration
1. Update settings store with sound preferences (use existing key-value system)
2. Add sound settings to frontendToDbKeyMap
3. Extend TimerSettingsModal UI components  
4. Update default settings and reset function

### Phase 3: Timer Integration
1. Integrate audio service into timer store
2. Add sound triggers to timer completion handlers
3. Implement volume control and muting
4. Add sound preview functionality in settings

### Phase 4: Audio Assets
1. Create `public/sounds/` directory (following existing `public/icons/` pattern)
2. Source or create default sound files and place in `public/sounds/`
3. Optimize audio files for size and quality
4. Add fallback handling for missing files

### Phase 5: Testing & Polish
1. Test across different operating systems
2. Verify audio works in both dev and production builds
3. Implement user feedback for sound preferences
4. Add accessibility considerations

## Technical Implementation Details

### Audio Service Structure
```typescript
// src/composables/useTimerAudio.ts (following existing composable patterns)
export interface TimerAudioService {
  playSound(soundId: string, volume?: number): Promise<void>
  preloadSounds(): Promise<void>
  updateGlobalVolume(volume: number): void
  testSound(soundId: string): Promise<void>
  isEnabled: Computed<boolean>
  isLoading: Readonly<Ref<boolean>>
  loadError: Readonly<Ref<string | null>>
}
```

### Timer Integration Points
1. **Pomodoro Complete**: `handleTimerComplete()` when `timerType === 'pomodoro'`
2. **Break Complete**: `handleTimerComplete()` when `timerType` includes 'Break'
3. **Timer Start**: `toggleTimer()` when starting timer (optional)
4. **Session Complete**: End of full pomodoro session (optional)

### Error Handling Strategy
- Graceful degradation when audio fails
- User notification for audio issues
- Fallback to visual notifications only
- Logging for debugging audio problems

## Future Enhancements
- Custom sound file upload
- Sound themes/packs
- Advanced audio effects
- Integration with system notifications
- Accessibility features (visual alternatives)

## Estimated Implementation Time
- **Phase 1-2**: 4-6 hours (core system + settings)
- **Phase 3**: 2-3 hours (timer integration)
- **Phase 4**: 2-3 hours (audio assets)
- **Phase 5**: 2-4 hours (testing & polish)
- **Total**: 10-16 hours

## Dependencies
- No new external dependencies required
- Uses existing Electron and Vue.js capabilities
- Leverages current settings and timer architecture

## Detailed Code Implementation Examples

### 1. Audio Service Composable (`src/composables/useTimerAudio.ts`)

```typescript
import { ref, computed, readonly } from 'vue'
import { useSettingsStore } from '../stores/settingsStore'

interface SoundMap {
  [key: string]: HTMLAudioElement
}

export function useTimerAudio() {
  const settingsStore = useSettingsStore()
  const sounds = ref<SoundMap>({})
  const isLoading = ref(false)
  const loadError = ref<string | null>(null)

  // Sound file mappings
  const soundFiles = {
    'pomodoro-complete': '/sounds/pomodoro-complete.mp3',
    'break-complete': '/sounds/break-complete.mp3',
    'timer-start': '/sounds/timer-start.mp3',
    'session-complete': '/sounds/session-complete.mp3'
  }

  // Computed properties
  const isEnabled = computed(() => settingsStore.settings.timerSoundsEnabled)
  const globalVolume = computed(() => settingsStore.settings.soundVolume)

  // Preload all sound files
  async function preloadSounds(): Promise<void> {
    if (isLoading.value) return

    isLoading.value = true
    loadError.value = null

    try {
      const loadPromises = Object.entries(soundFiles).map(([key, path]) => {
        return new Promise<void>((resolve, reject) => {
          const audio = new Audio(path)
          audio.preload = 'auto'
          audio.volume = globalVolume.value

          audio.addEventListener('canplaythrough', () => {
            sounds.value[key] = audio
            resolve()
          })

          audio.addEventListener('error', (e) => {
            console.warn(`Failed to load sound: ${key}`, e)
            // Don't reject - allow app to continue without this sound
            resolve()
          })
        })
      })

      await Promise.all(loadPromises)
      console.log('✅ Audio preloading completed')
    } catch (error) {
      loadError.value = 'Failed to load audio files'
      console.error('❌ Audio preloading failed:', error)
    } finally {
      isLoading.value = false
    }
  }

  // Play a specific sound
  async function playSound(soundId: string, volume?: number): Promise<void> {
    if (!isEnabled.value) {
      console.log('🔇 Audio disabled, skipping sound:', soundId)
      return
    }

    const audio = sounds.value[soundId]
    if (!audio) {
      console.warn('🔊 Sound not found:', soundId)
      return
    }

    try {
      // Reset audio to beginning
      audio.currentTime = 0

      // Set volume (use provided volume or global setting)
      audio.volume = volume !== undefined ? volume : globalVolume.value

      // Play the sound
      await audio.play()
      console.log('🔊 Played sound:', soundId)
    } catch (error) {
      console.error('❌ Failed to play sound:', soundId, error)
    }
  }

  // Update volume for all loaded sounds
  function updateGlobalVolume(volume: number): void {
    Object.values(sounds.value).forEach(audio => {
      audio.volume = volume
    })
  }

  // Test a sound (for settings preview)
  async function testSound(soundId: string): Promise<void> {
    await playSound(soundId, globalVolume.value)
  }

  return {
    // State
    isLoading: readonly(isLoading),
    loadError: readonly(loadError),
    isEnabled,

    // Methods
    preloadSounds,
    playSound,
    testSound,
    updateGlobalVolume
  }
}
```

### 2. Timer Store Integration

```typescript
// Add to src/stores/timerStore.ts (after line 5)
import { useTimerAudio } from '../composables/useTimerAudio'

export const useTimerStore = defineStore('timer', () => {
    // ... existing code ...

    // Initialize audio service
    const audio = useTimerAudio()

    // Initialize audio in initialize() function (line 812-823)
    async function initialize() {
        console.log('🚀 [TimerStore] initialize called')
        try {
            await loadSettings()
            await loadActiveSession()
            await audio.preloadSounds() // Add this line
            console.log('✅ [TimerStore] Initialization completed successfully')
        } catch (error) {
            console.error('❌ [TimerStore] Initialization failed:', error)
        }
    }

    // Modified handleTimerComplete function (lines 466-552)
    async function handleTimerComplete() {
        console.log('🏁 [TimerStore] handleTimerComplete called for type:', state.value.timerType)

        // Play completion sound based on timer type (add at beginning of function)
        if (state.value.timerType === 'pomodoro') {
            await audio.playSound('pomodoro-complete')
            console.log('🎯 [TimerStore] Completing pomodoro cycle in database...')
            // ... existing pomodoro completion logic ...
        } else if (state.value.timerType === 'shortBreak' || state.value.timerType === 'longBreak') {
            await audio.playSound('break-complete')
            console.log('☕ [TimerStore] Completing break cycle...')
            // ... existing break completion logic ...
        }

        // ... rest of existing completion logic (lines 469-552) ...
    }

    // Modified toggleTimer function (lines 262-319) - add sound on start
    async function toggleTimer(): Promise<{ success: boolean; reason?: string }> {
        // ... existing validation logic (lines 262-272) ...

        if (!state.value.isRunning) {
            // Starting timer - play start sound (add after line 274)
            await audio.playSound('timer-start')
            
            // Mark that timer was started for this cycle (line 279)
            state.value.wasTimerStartedThisCycle = true
            
            // ... existing start logic (lines 281-305) ...
        } else {
            // Pausing timer - no sound needed
            // ... existing pause logic (lines 307-312) ...
        }

        // ... rest of existing toggle logic (lines 314-319) ...
    }

    return {
        // ... existing exports ...
        audio // Export audio service for component access
    }
})
```

### 3. Settings Store Extension - Three Required Changes

```typescript
// 1. Update AppSettings interface (lines 7-38)
export interface AppSettings {
  // ... existing settings (lines 8-37)

  // Timer sound settings (add these)
  timerSoundsEnabled: boolean
  pomodoroCompleteSound: string
  breakCompleteSound: string
  timerStartSound: string
  soundVolume: number
}

// 2. Add to frontendToDbKeyMap (lines 207-227)
const frontendToDbKeyMap = {
  // ... existing mappings (lines 208-226)
  timerSoundsEnabled: 'timer_sounds_enabled',
  pomodoroCompleteSound: 'pomodoro_complete_sound',
  breakCompleteSound: 'break_complete_sound', 
  timerStartSound: 'timer_start_sound',
  soundVolume: 'sound_volume'
}

// 3. Update default settings (lines 42-71)
const settings = ref<AppSettings>({
  // ... existing defaults (lines 43-70)
  
  // Sound defaults (add these)
  timerSoundsEnabled: true,
  pomodoroCompleteSound: 'default',
  breakCompleteSound: 'default',
  timerStartSound: 'none',
  soundVolume: 0.7
})

// 4. Update resetToDefaults function (lines 107-131)
async function resetToDefaults() {
  const defaultSettings: AppSettings = {
    // ... existing defaults (lines 109-127)
    
    // Sound defaults (add these)
    timerSoundsEnabled: true,
    pomodoroCompleteSound: 'default',
    breakCompleteSound: 'default',
    timerStartSound: 'none',
    soundVolume: 0.7
  }
  settings.value = defaultSettings
  await saveAllSettingsToDatabase(defaultSettings)
}
```

### 4. Timer Settings Modal Extension - Two Required Changes

#### A. Template Update (add after line 122, before modal footer):
```vue
<!-- Add new Sound Settings section to TimerSettingsModal.vue template -->
<div class="form-section">
  <h4 class="section-title">Sound Notifications</h4>
  <div class="checkbox-grid">
    <div class="checkbox-field">
      <div class="checkbox-wrapper">
        <input
          type="checkbox"
          v-model="settings.timerSoundsEnabled"
          class="form-checkbox"
          id="timer-sounds-enabled"
        />
        <label for="timer-sounds-enabled" class="checkbox-label">
          <span class="checkbox-title">Enable Timer Sounds</span>
          <span class="checkbox-description">Play notification sounds when timers complete</span>
        </label>
      </div>
    </div>

    <div v-if="settings.timerSoundsEnabled" class="sound-settings">
      <div class="form-field">
        <label>Pomodoro Complete Sound</label>
        <div class="sound-selector">
          <select v-model="settings.pomodoroCompleteSound" class="form-select">
            <option value="default">Default Bell</option>
            <option value="chime">Soft Chime</option>
            <option value="ding">Classic Ding</option>
            <option value="none">No Sound</option>
          </select>
          <button @click="testSound('pomodoro-complete')" class="btn btn-test">Test</button>
        </div>
      </div>

      <div class="form-field">
        <label>Break Complete Sound</label>
        <div class="sound-selector">
          <select v-model="settings.breakCompleteSound" class="form-select">
            <option value="default">Default Bell</option>
            <option value="chime">Soft Chime</option>
            <option value="ding">Classic Ding</option>
            <option value="none">No Sound</option>
          </select>
          <button @click="testSound('break-complete')" class="btn btn-test">Test</button>
        </div>
      </div>

      <div class="form-field">
        <label>Volume</label>
        <div class="volume-control">
          <input
            type="range"
            min="0"
            max="1"
            step="0.1"
            v-model.number="settings.soundVolume"
            class="volume-slider"
          />
          <span class="volume-display">{{ Math.round(settings.soundVolume * 100) }}%</span>
        </div>
      </div>
    </div>
  </div>
</div>
```

#### B. Script Updates:
```typescript
// 1. Add import after line 138
import { useTimerAudio } from '../../composables/useTimerAudio'

// 2. Update props to include sound settings (lines 142-167)
props: {
  // ... existing props
  timerSoundsEnabled: { type: Boolean, default: true },
  pomodoroCompleteSound: { type: String, default: 'default' },
  breakCompleteSound: { type: String, default: 'default' },
  timerStartSound: { type: String, default: 'none' },
  soundVolume: { type: Number, default: 0.7 }
}

// 3. Update settings reactive object (lines 171-178)
const settings = reactive({
  // ... existing settings
  timerSoundsEnabled: props.timerSoundsEnabled,
  pomodoroCompleteSound: props.pomodoroCompleteSound, 
  breakCompleteSound: props.breakCompleteSound,
  timerStartSound: props.timerStartSound,
  soundVolume: props.soundVolume
})

// 4. Add audio functionality in setup (after line 178)
const audio = useTimerAudio()

const testSound = async (soundId: string) => {
  await audio.testSound(soundId)
}

watch(() => settings.soundVolume, (newVolume) => {
  audio.updateGlobalVolume(newVolume)
})

// 5. Update return statement (lines 253-261)
return {
  // ... existing returns
  testSound
}
```

<style scoped>
.sound-settings {
  margin-top: 1rem;
  padding-left: 1.5rem;
  border-left: 2px solid var(--border-color);
}

.sound-selector {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.btn-test {
  padding: 0.25rem 0.75rem;
  font-size: 0.875rem;
  background: var(--accent-color);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.btn-test:hover {
  background: var(--accent-color-hover);
}

.volume-control {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.volume-slider {
  flex: 1;
  height: 4px;
  background: var(--border-color);
  border-radius: 2px;
  outline: none;
}

.volume-display {
  min-width: 3rem;
  text-align: right;
  font-size: 0.875rem;
  color: var(--text-secondary);
}
</style>
```

## Additional Required Changes

### 1. Update PomodoroTimer.vue Component Props
```typescript
// Update src/components/timer/PomodoroTimer.vue 
// Add props for sound settings in TimerSettingsModal usage (line 33-36):
<TimerSettingsModal 
  v-if="showSettings" 
  @close="showSettings = false" 
  @update-settings="handleUpdateSettings"
  :pomodoroTime="timerStore.pomodoroTime" 
  :shortBreakTime="timerStore.shortBreakTime"
  :longBreakTime="timerStore.longBreakTime" 
  :longBreakInterval="timerStore.longBreakInterval"
  :autoStartBreaks="timerStore.autoStartBreaks" 
  :autoStartPomodoros="timerStore.autoStartPomodoros"
  :timerSoundsEnabled="settingsStore.settings.timerSoundsEnabled"
  :pomodoroCompleteSound="settingsStore.settings.pomodoroCompleteSound"
  :breakCompleteSound="settingsStore.settings.breakCompleteSound"
  :timerStartSound="settingsStore.settings.timerStartSound"
  :soundVolume="settingsStore.settings.soundVolume"
/>
```

### 2. Update handleUpdateSettings Function
```typescript
// In PomodoroTimer.vue, update handleUpdateSettings (lines 272-289):
const handleUpdateSettings = async (newSettings: {
  pomodoroTime: number
  shortBreakTime: number
  longBreakTime: number
  longBreakInterval: number
  autoStartBreaks: boolean
  autoStartPomodoros: boolean
  timerSoundsEnabled: boolean
  pomodoroCompleteSound: string
  breakCompleteSound: string
  timerStartSound: string
  soundVolume: number
}) => {
  try {
    // Update timer settings
    await timerStore.updateSettings({
      pomodoroTime: newSettings.pomodoroTime,
      shortBreakTime: newSettings.shortBreakTime,
      longBreakTime: newSettings.longBreakTime,
      longBreakInterval: newSettings.longBreakInterval,
      autoStartBreaks: newSettings.autoStartBreaks,
      autoStartPomodoros: newSettings.autoStartPomodoros
    })
    
    // Update sound settings in settings store
    await settingsStore.updateSettings({
      timerSoundsEnabled: newSettings.timerSoundsEnabled,
      pomodoroCompleteSound: newSettings.pomodoroCompleteSound,
      breakCompleteSound: newSettings.breakCompleteSound,
      timerStartSound: newSettings.timerStartSound,
      soundVolume: newSettings.soundVolume
    })
    
    showSettings.value = false
  } catch (error) {
    console.error('Failed to update settings:', error)
  }
}
```

### 3. Add CSS Styles
```css
/* Add to TimerSettingsModal.vue styles section */
.sound-settings {
  margin-top: 1rem;
  padding-left: 1.5rem;
  border-left: 2px solid var(--color-border-secondary);
}

.sound-selector {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.btn-test {
  padding: 0.25rem 0.75rem;
  font-size: 0.875rem;
  background: var(--color-btn-primary-bg);
  color: var(--color-btn-primary-text);
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn-test:hover {
  background: var(--color-btn-primary-hover);
}

.volume-control {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.volume-slider {
  flex: 1;
  height: 4px;
  background: var(--color-border-secondary);
  border-radius: 2px;
  outline: none;
}

.volume-display {
  min-width: 3rem;
  text-align: right;
  font-size: 0.875rem;
  color: var(--color-text-secondary);
}
```

## No Database Migrations Required
The existing `settings` table with key-value pairs is sufficient. No schema changes needed.
