// stores/settingsStore.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useElectronAPI } from '../useElectronAPI'
import { applyTheme, type ThemeType } from '../utils/themeUtils'

export interface AppSettings {
  // Timer display settings
  showTimerInTitleBar: boolean
  titleBarTimerFormat: 'minimal' | 'detailed' // "25:00" vs "25:00 - Pomodoro"

  // Clock display settings
  showClockInTitleBar: boolean

  // UI preferences
  theme: ThemeType
  language: string
  userName: string

  // Auto-save preferences
  autoSaveInterval: number // minutes

  // Sync settings (replaces old backup system)
  syncDirectory: string | null
  autoSyncEnabled: boolean
  syncInterval: number // minutes
  syncDebounceTime: number // milliseconds

  // Discord Rich Presence preferences
  discordRichPresenceEnabled: boolean
  discordShowSessionDetails: boolean
  discordShowTimer: boolean
  discordShowPomodoroCount: boolean
  discordShowElapsedTime: boolean
  discordCustomIdleMessage: string
  discordCustomStudyMessage: string
  discordShowAppName: boolean

  // Timer sound settings
  timerSoundsEnabled: boolean
  pomodoroCompleteSound: string
  breakCompleteSound: string
  timerStartSound: string
  soundVolume: number
}

export const useSettingsStore = defineStore('settings', () => {
  // ===== STATE =====
  const settings = ref<AppSettings>({
    // Default settings
    showTimerInTitleBar: true,
    titleBarTimerFormat: 'detailed',

    // Clock defaults
    showClockInTitleBar: false,

    theme: 'light',
    language: 'en',
    userName: '',

    autoSaveInterval: 1,

    // Sync defaults (replaces old backup system)
    syncDirectory: null,
    autoSyncEnabled: true,
    syncInterval: 30, // 30 minutes
    syncDebounceTime: 5000, // 5 seconds

    // Discord Rich Presence defaults
    discordRichPresenceEnabled: false,
    discordShowSessionDetails: true,
    discordShowTimer: true,
    discordShowPomodoroCount: true,
    discordShowElapsedTime: true,
    discordCustomIdleMessage: '',
    discordCustomStudyMessage: '',
    discordShowAppName: true,

    // Timer sound defaults
    timerSoundsEnabled: true,
    pomodoroCompleteSound: 'default',
    breakCompleteSound: 'default',
    timerStartSound: 'none',
    soundVolume: 0.7
  })



  // ===== COMPUTED =====
  const showTimerInTitleBar = computed(() => settings.value.showTimerInTitleBar)
  const titleBarTimerFormat = computed(() => settings.value.titleBarTimerFormat)
  const showClockInTitleBar = computed(() => settings.value.showClockInTitleBar)
  const currentTheme = computed(() => settings.value.theme)
  
  // ===== ACTIONS =====
  async function updateSetting<K extends keyof AppSettings>(key: K, value: AppSettings[K]) {
    settings.value[key] = value

    // Apply theme immediately if theme setting changed
    if (key === 'theme') {
      applyTheme(value as ThemeType)
    }

    // Save to database via IPC
    await saveSettingToDatabase(key, value)
  }
  
  async function updateSettings(newSettings: Partial<AppSettings>) {
    const oldTheme = settings.value.theme
    Object.assign(settings.value, newSettings)

    // Apply theme if it changed
    if (newSettings.theme && newSettings.theme !== oldTheme) {
      applyTheme(newSettings.theme)
    }

    // Save all changed settings to database
    await saveAllSettingsToDatabase(newSettings)
  }
  
  async function resetToDefaults() {
    const defaultSettings: AppSettings = {
      showTimerInTitleBar: true,
      titleBarTimerFormat: 'detailed' as const,
      showClockInTitleBar: false,
      theme: 'light' as const,
      language: 'en',
      userName: '',
      autoSaveInterval: 1,
      syncDirectory: null,
      autoSyncEnabled: true,
      syncInterval: 30,
      syncDebounceTime: 5000,
      discordRichPresenceEnabled: false,
      discordShowSessionDetails: true,
      discordShowTimer: true,
      discordShowPomodoroCount: true,
      discordShowElapsedTime: true,
      discordCustomIdleMessage: '',
      discordCustomStudyMessage: '',
      discordShowAppName: true,
      timerSoundsEnabled: true,
      pomodoroCompleteSound: 'default',
      breakCompleteSound: 'default',
      timerStartSound: 'none',
      soundVolume: 0.7
    }
    settings.value = defaultSettings
    await saveAllSettingsToDatabase(defaultSettings)
  }
  


  // ===== PERSISTENCE =====
  async function saveSettingToDatabase<K extends keyof AppSettings>(key: K, value: AppSettings[K]) {
    try {
      const db = useElectronAPI()
      
      // Map frontend setting keys to database keys
      const dbKey = mapFrontendKeyToDbKey(key)
      const category = getSettingCategory(key)
      
      await db.settings.set(dbKey, value, category)
      console.log(`Setting ${key} saved to database:`, value)
      
    } catch (error) {
      console.error(`Failed to save setting ${key}:`, error)
      throw error
    }
  }

  async function saveAllSettingsToDatabase(settingsToSave: Partial<AppSettings>) {
    try {
      const db = useElectronAPI()
      
      for (const [key, value] of Object.entries(settingsToSave)) {
        const dbKey = mapFrontendKeyToDbKey(key as keyof AppSettings)
        const category = getSettingCategory(key as keyof AppSettings)
        await db.settings.set(dbKey, value, category)
        
      }
      
      console.log('Settings saved to database:', settingsToSave)
    } catch (error) {
      console.error('Failed to save settings to database:', error)
      throw error
    }
  }

  async function loadFromDatabase() {
    try {
      const db = useElectronAPI()
      
      // Load all settings from database
      const allSettings = await db.settings.getAll()
      
      if (allSettings && allSettings.length > 0) {
        const settingsMap = new Map(allSettings.map((s: any) => [s.key, s.value]))
        
        // Map database keys back to frontend keys and update settings
        const loadedSettings: Partial<AppSettings> = {}
        
        for (const [frontendKey, dbKey] of Object.entries(frontendToDbKeyMap)) {
          if (settingsMap.has(dbKey)) {
            ;(loadedSettings as any)[frontendKey] = settingsMap.get(dbKey)
          }
        }
        
        // Migration: Convert 'system' theme to 'light' theme
        if ((loadedSettings as any).theme === 'system') {
          console.log('Migrating system theme to light theme')
          ;(loadedSettings as any).theme = 'light'
        }
        
        Object.assign(settings.value, loadedSettings)
        console.log('Settings loaded from database:', loadedSettings)
      } else {
        console.log('No settings found in database, using defaults')
      }
    } catch (error) {
      console.error('Failed to load settings from database:', error)
    }
  }

  // Helper functions for key mapping
  const frontendToDbKeyMap = {
    showTimerInTitleBar: 'showTimerInTitleBar',
    titleBarTimerFormat: 'titleBarTimerFormat',
    showClockInTitleBar: 'showClockInTitleBar',
    theme: 'theme',
    language: 'language',
    userName: 'userName',
    autoSaveInterval: 'autoSaveInterval',
    syncDirectory: 'syncDirectory',
    autoSyncEnabled: 'autoSyncEnabled',
    syncInterval: 'syncInterval',
    syncDebounceTime: 'syncDebounceTime',
    discordRichPresenceEnabled: 'discordRichPresenceEnabled',
    discordShowSessionDetails: 'discordShowSessionDetails',
    discordShowTimer: 'discordShowTimer',
    discordShowPomodoroCount: 'discordShowPomodoroCount',
    discordShowElapsedTime: 'discordShowElapsedTime',
    discordCustomIdleMessage: 'discordCustomIdleMessage',
    discordCustomStudyMessage: 'discordCustomStudyMessage',
    discordShowAppName: 'discordShowAppName',
    timerSoundsEnabled: 'timer_sounds_enabled',
    pomodoroCompleteSound: 'pomodoro_complete_sound',
    breakCompleteSound: 'break_complete_sound',
    timerStartSound: 'timer_start_sound',
    soundVolume: 'sound_volume'
  }

  function mapFrontendKeyToDbKey(key: keyof AppSettings): string {
    return frontendToDbKeyMap[key] || key
  }

  function getSettingCategory(key: keyof AppSettings): string {
    if (key.includes('discord') || key.includes('Discord')) return 'discord'
    if (key.includes('timer') || key.includes('Timer')) return 'timer'
    if (key.includes('clock') || key.includes('Clock')) return 'ui'
    if (key.includes('sync') || key.includes('Sync')) return 'sync'
    if (key === 'theme') return 'ui'
    return 'general'
  }

  
  // ===== INITIALIZATION =====
  async function initialize() {
    await loadFromDatabase()

    // Apply initial theme
    applyTheme(settings.value.theme)

    // Initialize Discord Rich Presence
    await initializeDiscord()
  }

  // Initialize Discord Rich Presence based on settings
  async function initializeDiscord() {
    const db = useElectronAPI()

    try {
      console.log('🎮 [SettingsStore] Initializing Discord with settings:', {
        enabled: settings.value.discordRichPresenceEnabled,
        showDetails: settings.value.discordShowSessionDetails,
        showTimer: settings.value.discordShowTimer,
        showPomodoro: settings.value.discordShowPomodoroCount
      })

      if (settings.value.discordRichPresenceEnabled) {
        console.log('🎮 [SettingsStore] Discord enabled, initializing...')
        await db.discord.initialize()
        await db.discord.setEnabled(true)

        // Initialize Discord settings with default values
        await db.discord.updateSettings({
          showNoteTaking: true,
          showBookWriting: true,
          showBookNames: true,
          showTimer: true,
          showSettings: true,
          showTimerDetails: true,
          showPomodoroCount: true,
          showSessionName: true,
          customIdleMessage: '',
          customActiveMessage: '',
          idleTimeout: 3
        })

        await db.discord.setActiveState()
        console.log('🎮 [SettingsStore] Discord initialized successfully')
      } else {
        console.log('🎮 [SettingsStore] Discord disabled in settings')
      }
    } catch (error) {
      console.error('🎮 [SettingsStore] Failed to initialize Discord:', error)
    }
  }

  function cleanup() {
    // No cleanup needed since system theme watcher was removed
  }

  // ===== PUBLIC API =====
  return {
    // State
    settings: computed(() => settings.value),

    // Computed getters
    showTimerInTitleBar,
    titleBarTimerFormat,
    showClockInTitleBar,
    currentTheme,

    // Actions
    updateSetting,
    updateSettings,
    resetToDefaults,
    initialize,
    cleanup
  }
})