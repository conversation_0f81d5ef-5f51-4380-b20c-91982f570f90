<template>
  <div class="title-clock">
    <span class="clock-time">{{ currentTime }}</span>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

// Current time display
const currentTime = ref('')
let timeInterval: NodeJS.Timeout | null = null

// Update time display
function updateTime() {
  const now = new Date()
  currentTime.value = now.toLocaleTimeString([], { 
    hour: '2-digit', 
    minute: '2-digit',
    hour12: false 
  })
}

// Lifecycle hooks
onMounted(() => {
  updateTime()
  timeInterval = setInterval(updateTime, 1000)
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
    timeInterval = null
  }
})
</script>

<style scoped>
.title-clock {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2px 8px;
  font-size: 11px;
  font-weight: 600;
  color: var(--color-text-primary);
  height: 20px;
  position: relative;
  /* No border or background - plain text on title bar background */
}

.clock-time {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  letter-spacing: 0.5px;
}
</style>
